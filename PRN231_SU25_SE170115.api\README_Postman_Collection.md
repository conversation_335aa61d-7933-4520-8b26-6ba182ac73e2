# Handbag API Tests - Postman Collection

## M<PERSON> tả
Collection này được tối ưu hóa để test API Handbag với JWT authentication. Sử dụng biến để giảm thiểu thay đổi cần thiết khi chạy test.

## Cách sử dụng

### 1. Import Collection
- Import file `Postman_Collection_Handbag_API_Tests.json` vào Postman
- Collection sẽ tự động thiết lập các biến cần thiết

### 2. Thiết lập Environment (Tùy chọn)
Chỉ cần thay đổi `base_url` nếu server của bạn chạy ở địa chỉ khác:
- Tạo Environment mới hoặc sử dụng Global variables
- Thiết lập biến `base_url` (mặc định: `https://localhost:7176`)

### 3. Chạy Tests

#### Chạy tuần tự (Khuyến nghị):
1. **Login Success** - Đăng nhập và lưu token
2. **Create Handbag** - Tạo handbag mới và lưu ID
3. **Update Handbag** - Cập nhật handbag vừa tạo
4. **Delete Handbag** - Xóa handbag vừa tạo
5. **Get All Handbags** - Lấy danh sách và lưu ID đầu tiên
6. **Get Handbag by ID** - Lấy handbag theo ID

#### Chạy Collection Runner:
- Chọn toàn bộ collection và chạy
- Tests sẽ tự động sử dụng ID động từ các response

## Biến được sử dụng

### Biến tự động (Không cần thay đổi):
- `auth_token` - JWT token từ login
- `user_role` - Role của user
- `created_handbag_id` - ID của handbag vừa tạo
- `handbag_id_to_update` - ID để update (tự động từ create)
- `handbag_id_to_delete` - ID để delete (tự động từ create)
- `existing_handbag_id` - ID để test GET by ID (từ GET all)

### Biến có thể thay đổi:
- `base_url` - URL của API server (mặc định: https://localhost:7176)

## Tính năng tối ưu

### 1. Tự động lưu Token
- Login thành công sẽ tự động lưu JWT token
- Tất cả request sau sẽ sử dụng token này

### 2. Tự động lưu ID
- Create handbag sẽ lưu ID cho update/delete
- Get all handbags sẽ lưu ID đầu tiên cho GET by ID

### 3. Logging
- Console log hiển thị token và ID được lưu
- Dễ debug khi có vấn đề

### 4. Validation đầy đủ
- Kiểm tra status code
- Kiểm tra response structure
- Kiểm tra JWT format
- Kiểm tra authorization header

## Test Cases

1. **Login Success** - Test đăng nhập thành công
2. **Login Failed** - Test đăng nhập thất bại
3. **Create Handbag** - Test tạo handbag mới (cần admin token)
4. **Update Handbag** - Test cập nhật handbag (cần admin token)
5. **Delete Handbag** - Test xóa handbag (cần admin token)
6. **Get All Handbags** - Test lấy danh sách handbag
7. **Get Handbag by ID** - Test lấy handbag theo ID

## Bonus Tests (Tùy chọn)
- Login as Member
- Unauthorized Create (member token)
- Get without Token (401 test)

## Debug Token Issue

### Nếu token không được lưu:

1. **Sử dụng Login_Test_Only.json**:
   - Import file `Login_Test_Only.json`
   - Chạy test này trước để debug
   - Xem Console log để kiểm tra response

2. **Kiểm tra Console Log**:
   - Mở Postman Console (View → Show Postman Console)
   - Chạy Login test
   - Tìm các message:
     - `✅ Token found:` - Token có trong response
     - `✅ Token stored successfully: YES` - Token đã lưu thành công
     - `❌ No token found` - Không có token trong response

3. **Các vấn đề thường gặp**:
   - **Server không chạy**: Kiểm tra `base_url`
   - **Credentials sai**: Kiểm tra email/password trong database
   - **Response format khác**: Xem raw response trong Console
   - **CORS issue**: Kiểm tra server CORS settings

4. **Kiểm tra Environment Variables**:
   - Vào Environment tab trong Postman
   - Xem có biến `auth_token` không
   - Nếu không có, token không được lưu

## Lưu ý
- Đảm bảo API server đang chạy trước khi test
- Thay đổi `base_url` nếu server chạy ở port khác
- Chạy tests theo thứ tự để đảm bảo dữ liệu đúng
- Kiểm tra Console log để debug nếu cần
- Sử dụng `Login_Test_Only.json` để debug riêng login issue
