{"id": "handbag-api-environment", "name": "Handbag API Environment", "values": [{"key": "base_url", "value": "https://localhost:7176", "description": "Base URL for the API (HTTPS)", "enabled": true}, {"key": "base_url_http", "value": "http://localhost:5038", "description": "Base URL for the API (HTTP)", "enabled": true}, {"key": "admin_token", "value": "", "description": "JWT token for administrator user (auto-populated from login)", "enabled": true}, {"key": "member_token", "value": "", "description": "JWT token for member user (auto-populated from login)", "enabled": true}, {"key": "admin_role", "value": "", "description": "Role of admin user (auto-populated from login)", "enabled": true}, {"key": "handbag_id_to_update", "value": "1", "description": "ID of handbag to update in tests", "enabled": true}, {"key": "handbag_id_to_delete", "value": "2", "description": "ID of handbag to delete in tests", "enabled": true}, {"key": "existing_handbag_id", "value": "1", "description": "ID of existing handbag for GET by ID test", "enabled": true}, {"key": "created_handbag_id", "value": "", "description": "ID of newly created handbag (auto-populated)", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2025-01-18T00:00:00.000Z", "_postman_exported_using": "Postman/10.0.0"}