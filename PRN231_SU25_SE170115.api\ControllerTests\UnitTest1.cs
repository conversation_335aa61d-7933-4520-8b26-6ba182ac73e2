using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Moq;
using Services;
using Model;
using DAL.Models;
using UOW;
using PRN231_SU25_SE170115.api.Controllers;
using Middleware;

namespace ControllerTests;

public class AuthControllerTests
{
    private readonly Mock<IAuthService> _mockAuthService;
    private readonly AuthController _controller;

    public AuthControllerTests()
    {
        _mockAuthService = new Mock<IAuthService>();
        _controller = new AuthController(_mockAuthService.Object);
    }

    [Fact]
    public async Task Login_ValidCredentials_ReturnsOkWithToken()
    {
        // Arrange
        var loginModel = new LoginModel { Email = "<EMAIL>", Password = "password" };
        var user = new SystemAccount { Username = "testuser", Role = 1 };
        var authResponse = new AuthResponse { Token = "test-token", Role = "administrator" };

        _mockAuthService.Setup(s => s.Login(loginModel)).ReturnsAsync(user);
        _mockAuthService.Setup(s => s.GenerateJwtToken(user.Username, user.Role)).ReturnsAsync(authResponse);

        // Act
        var result = await _controller.Login(loginModel);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var response = okResult.Value;
        Assert.NotNull(response);

        // Verify the response structure
        var tokenProperty = response.GetType().GetProperty("token");
        var roleProperty = response.GetType().GetProperty("role");
        Assert.NotNull(tokenProperty);
        Assert.NotNull(roleProperty);
        Assert.Equal("test-token", tokenProperty.GetValue(response));
        Assert.Equal("administrator", roleProperty.GetValue(response));
    }

    [Fact]
    public async Task Login_InvalidCredentials_ThrowsUnauthorizedTokenException()
    {
        // Arrange
        var loginModel = new LoginModel { Email = "<EMAIL>", Password = "wrongpassword" };
        _mockAuthService.Setup(s => s.Login(loginModel)).ReturnsAsync((SystemAccount)null);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<UnauthorizedTokenException>(() => _controller.Login(loginModel));
        Assert.Equal("Invalid email or password", exception.Message);
    }

    [Fact]
    public async Task Login_NullUser_ThrowsUnauthorizedTokenException()
    {
        // Arrange
        var loginModel = new LoginModel { Email = "<EMAIL>", Password = "password" };
        _mockAuthService.Setup(s => s.Login(loginModel)).ReturnsAsync((SystemAccount?)null);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<UnauthorizedTokenException>(() => _controller.Login(loginModel));
        Assert.Equal("Invalid email or password", exception.Message);
    }

    [Fact]
    public async Task Login_EmptyEmail_ThrowsUnauthorizedTokenException()
    {
        // Arrange
        var loginModel = new LoginModel { Email = "", Password = "password" };
        _mockAuthService.Setup(s => s.Login(loginModel)).ReturnsAsync((SystemAccount?)null);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<UnauthorizedTokenException>(() => _controller.Login(loginModel));
        Assert.Equal("Invalid email or password", exception.Message);
    }

    [Fact]
    public async Task Login_EmptyPassword_ThrowsUnauthorizedTokenException()
    {
        // Arrange
        var loginModel = new LoginModel { Email = "<EMAIL>", Password = "" };
        _mockAuthService.Setup(s => s.Login(loginModel)).ReturnsAsync((SystemAccount?)null);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<UnauthorizedTokenException>(() => _controller.Login(loginModel));
        Assert.Equal("Invalid email or password", exception.Message);
    }

    [Fact]
    public async Task Login_ServiceThrowsException_ExceptionPropagates()
    {
        // Arrange
        var loginModel = new LoginModel { Email = "<EMAIL>", Password = "password" };
        _mockAuthService.Setup(s => s.Login(loginModel)).ThrowsAsync(new Exception("Database error"));

        // Act & Assert
        var exception = await Assert.ThrowsAsync<Exception>(() => _controller.Login(loginModel));
        Assert.Equal("Database error", exception.Message);
    }
}