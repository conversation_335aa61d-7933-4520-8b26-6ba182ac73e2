{"info": {"name": "Login Test Only - Debug", "description": "Simple login test to debug token storage issue", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Login Test", "event": [{"listen": "test", "script": {"exec": ["// Debug: Log everything", "console.log('=== LOGIN TEST DEBUG ===');", "console.log('Response Status:', pm.response.code);", "console.log('Response Headers:', JSON.stringify(pm.response.headers.toJSON(), null, 2));", "console.log('Response Body (raw):', pm.response.text());", "", "// Try to parse JSON", "try {", "    const responseJson = pm.response.json();", "    console.log('Parsed JSON:', JSON.stringify(response<PERSON><PERSON>, null, 2));", "    ", "    // Check if token exists", "    if (responseJson.token) {", "        console.log('✅ <PERSON><PERSON> found:', responseJson.token.substring(0, 30) + '...');", "        ", "        // Try to store token", "        pm.environment.set('auth_token', responseJson.token);", "        pm.environment.set('user_role', responseJson.role);", "        ", "        // Verify storage", "        const storedToken = pm.environment.get('auth_token');", "        console.log('✅ Token stored successfully:', storedToken ? 'YES' : 'NO');", "        console.log('✅ Stored token preview:', storedToken ? storedToken.substring(0, 30) + '...' : 'NONE');", "        ", "        // Test assertions", "        pm.test('Status code is 200', function () {", "            pm.response.to.have.status(200);", "        });", "        ", "        pm.test('Token exists in response', function () {", "            pm.expect(responseJson.token).to.exist;", "            pm.expect(responseJson.token).to.not.be.empty;", "        });", "        ", "        pm.test('Token stored in environment', function () {", "            const token = pm.environment.get('auth_token');", "            pm.expect(token).to.exist;", "            pm.expect(token).to.equal(responseJson.token);", "        });", "        ", "    } else {", "        console.log('❌ No token found in response');", "        console.log('Available properties:', Object.keys(responseJson));", "    }", "    ", "} catch (error) {", "    console.log('❌ JSON Parse Error:', error.message);", "    console.log('Raw response:', pm.response.text());", "}", "", "console.log('=== END DEBUG ===');"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"123456\"\n}"}, "url": {"raw": "{{base_url}}/api/auth", "host": ["{{base_url}}"], "path": ["api", "auth"]}}}], "variable": [{"key": "base_url", "value": "https://localhost:7176", "type": "string"}]}