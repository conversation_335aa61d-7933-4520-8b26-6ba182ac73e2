# Handbag API Postman Tests - PRN231_SU25_SE170115

## Overview
This collection contains comprehensive Postman tests for the Handbag API with JWT authentication. It includes all 6 required test cases plus bonus tests for role-based access control.

## Files Included
1. **`Postman_Collection_Handbag_API_Tests.json`** - Main test collection
2. **`Postman_Environment_Handbag_API.json`** - Environment variables
3. **`Postman_Test_Scripts.js`** - Reusable test scripts
4. **`README_Postman_Tests.md`** - This documentation

## Test Cases Included

### Core Requirements (6 Tests)
1. **Login Success** - Tests successful authentication with admin credentials
2. **Login Failed** - Tests failed authentication with invalid credentials
3. **Create Handbag** - Tests creating handbag (authorized roles only)
4. **Update Handbag** - Tests updating existing handbag
5. **Delete Handbag** - Tests deleting handbag
6. **Get List / Get by ID** - Tests retrieving handbags (list and individual)

### Bonus Tests
- **Member Login** - Tests login with member role
- **Unauthorized Create** - Tests create with insufficient permissions (403)
- **No Token Access** - Tests access without JWT token (401)

## Quick Setup Instructions

### 1. Import Collection and Environment
1. Open Postman
2. Click **Import** button
3. Import `Postman_Collection_Handbag_API_Tests.json`
4. Import `Postman_Environment_Handbag_API.json`
5. Select the "Handbag API Environment" from the environment dropdown

### 2. Configure Environment Variables
The environment includes these customizable variables:

| Variable | Default Value | Description |
|----------|---------------|-------------|
| `base_url` | `https://localhost:7176` | HTTPS API base URL |
| `base_url_http` | `http://localhost:5038` | HTTP API base URL |
| `handbag_id_to_update` | `1` | ID for update tests |
| `handbag_id_to_delete` | `2` | ID for delete tests |
| `existing_handbag_id` | `1` | ID for GET by ID tests |

### 3. Quick URL Switching
To switch between different URLs quickly:

**For HTTPS (default):**
```
https://localhost:7176
```

**For HTTP:**
```
http://localhost:5038
```

**For IIS Express:**
```
http://localhost:4381
```

## Running the Tests

### Option 1: Run Entire Collection
1. Click on the collection name
2. Click **Run** button
3. Select all tests or specific ones
4. Click **Run Handbag API Tests**

### Option 2: Run Individual Tests
1. Click on individual test requests
2. Click **Send** button
3. Check the **Test Results** tab

## Test Credentials

The tests use these predefined credentials:

### Administrator (Full Access)
```json
{
  "email": "<EMAIL>",
  "password": "123456"
}
```

### Member (Read-Only Access)
```json
{
  "email": "<EMAIL>",
  "password": "123456"
}
```

## Expected Test Results

### ✅ Successful Tests
- **Login Success**: Status 200, returns JWT token and role
- **Create Handbag**: Status 200/201, creates new handbag
- **Update Handbag**: Status 200, updates existing handbag
- **Delete Handbag**: Status 200, returns success message
- **Get All Handbags**: Status 200, returns array of handbags
- **Get Handbag by ID**: Status 200, returns single handbag object

### ❌ Expected Failures (Security Tests)
- **Login Failed**: Status 404/500, no token returned
- **Unauthorized Create**: Status 403, permission denied
- **No Token Access**: Status 401, unauthorized

## Customization Guide

### 1. Change Base URL
Update the `base_url` environment variable:
```javascript
pm.environment.set('base_url', 'your-api-url-here');
```

### 2. Change Test Data
Modify the request bodies in the collection or update environment variables for handbag IDs.

### 3. Add Custom Tests
Copy test scripts from `Postman_Test_Scripts.js` and paste into the **Tests** tab of any request.

### 4. Quick Token Refresh
The collection automatically stores JWT tokens from login responses. To manually refresh:
1. Run the "Login Success" test
2. Token is automatically stored in `admin_token` variable
3. All subsequent requests use this token

## Troubleshooting

### Common Issues

**1. Connection Refused**
- Ensure the API is running
- Check the correct port in `base_url`
- Try switching between HTTP/HTTPS

**2. 401 Unauthorized**
- Run login test first to get fresh token
- Check if token is stored in environment variables
- Verify JWT token format

**3. 403 Forbidden**
- Check user role permissions
- Ensure using admin token for create/update/delete operations

**4. Test Failures**
- Check API response format matches expected structure
- Verify handbag IDs exist in database
- Ensure API is returning expected status codes

### Debug Tips
1. Check **Console** tab for detailed logs
2. Verify **Headers** include `Authorization: Bearer <token>`
3. Check **Environment** variables are set correctly
4. Use **Pre-request Script** to log variables:
   ```javascript
   console.log('Base URL:', pm.environment.get('base_url'));
   console.log('Token:', pm.environment.get('admin_token'));
   ```

## API Endpoints Tested

| Method | Endpoint | Auth Required | Roles Allowed |
|--------|----------|---------------|---------------|
| POST | `/api/auth` | No | N/A |
| GET | `/api/handbags` | Yes | All roles |
| GET | `/api/handbags/{id}` | Yes | All roles |
| POST | `/api/handbags` | Yes | Admin, Moderator |
| PUT | `/api/handbags/{id}` | Yes | Admin, Moderator |
| DELETE | `/api/handbags/{id}` | Yes | Admin, Moderator |

## Notes
- All tests include JWT token validation
- Tests automatically store and reuse authentication tokens
- Environment variables make it easy to switch between different API instances
- Collection includes both positive and negative test scenarios
- Scripts validate response status codes, message content, and data structure
