// ========================================
// POSTMAN TEST SCRIPTS FOR HANDBAG API
// ========================================
// Copy and paste these scripts into your Postman test tabs

// ========================================
// 1. LOGIN SUCCESS TEST SCRIPT
// ========================================
/*
// Test 1: Login Success
pm.test('Status code is 200', function () {
    pm.response.to.have.status(200);
});

pm.test('Response has token and role', function () {
    const responseJson = pm.response.json();
    pm.expect(responseJson).to.have.property('token');
    pm.expect(responseJson).to.have.property('role');
    pm.expect(responseJson.role).to.be.oneOf(['administrator', 'moderator', 'developer', 'member']);
});

pm.test('Token is valid JWT format', function () {
    const responseJson = pm.response.json();
    const token = responseJson.token;
    pm.expect(token).to.match(/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+$/);
});

// Store token for subsequent requests
if (pm.response.code === 200) {
    const responseJson = pm.response.json();
    pm.environment.set('admin_token', responseJson.token);
    pm.environment.set('admin_role', responseJson.role);
}
*/

// ========================================
// 2. LOGIN FAILED TEST SCRIPT
// ========================================
/*
// Test 2: Login Failed
pm.test('Status code indicates error', function () {
    pm.expect(pm.response.code).to.be.oneOf([400, 401, 404, 500]);
});

pm.test('No token in response', function () {
    try {
        const responseJson = pm.response.json();
        pm.expect(responseJson).to.not.have.property('token');
    } catch (e) {
        // Response might not be JSON for error cases
        pm.expect(true).to.be.true;
    }
});
*/

// ========================================
// 3. CREATE HANDBAG TEST SCRIPT
// ========================================
/*
// Test 3: Create Handbag Success
pm.test('Status code is 200 or 201', function () {
    pm.expect(pm.response.code).to.be.oneOf([200, 201]);
});

pm.test('Response contains success message or created handbag', function () {
    const responseJson = pm.response.json();
    pm.expect(responseJson).to.be.an('object');
    // Store created handbag ID if available
    if (responseJson.handbagId || responseJson.id) {
        pm.environment.set('created_handbag_id', responseJson.handbagId || responseJson.id);
    }
});

pm.test('JWT token was included in request', function () {
    pm.expect(pm.request.headers.get('Authorization')).to.include('Bearer');
});
*/

// ========================================
// 4. UPDATE HANDBAG TEST SCRIPT
// ========================================
/*
// Test 4: Update Handbag Success
pm.test('Status code is 200', function () {
    pm.response.to.have.status(200);
});

pm.test('Response contains success message or updated handbag', function () {
    const responseJson = pm.response.json();
    pm.expect(responseJson).to.be.an('object');
});

pm.test('JWT token was included in request', function () {
    pm.expect(pm.request.headers.get('Authorization')).to.include('Bearer');
});

pm.test('Request method is PUT', function () {
    pm.expect(pm.request.method).to.eql('PUT');
});
*/

// ========================================
// 5. DELETE HANDBAG TEST SCRIPT
// ========================================
/*
// Test 5: Delete Handbag Success
pm.test('Status code is 200', function () {
    pm.response.to.have.status(200);
});

pm.test('Response contains success message', function () {
    const responseJson = pm.response.json();
    pm.expect(responseJson).to.have.property('message');
    pm.expect(responseJson.message).to.include('Deleted successfully');
});

pm.test('JWT token was included in request', function () {
    pm.expect(pm.request.headers.get('Authorization')).to.include('Bearer');
});

pm.test('Request method is DELETE', function () {
    pm.expect(pm.request.method).to.eql('DELETE');
});
*/

// ========================================
// 6A. GET ALL HANDBAGS TEST SCRIPT
// ========================================
/*
// Test 6a: Get All Handbags
pm.test('Status code is 200', function () {
    pm.response.to.have.status(200);
});

pm.test('Response is an array of handbags', function () {
    const responseJson = pm.response.json();
    pm.expect(responseJson).to.be.an('array');
});

pm.test('JWT token was included in request', function () {
    pm.expect(pm.request.headers.get('Authorization')).to.include('Bearer');
});

pm.test('Request method is GET', function () {
    pm.expect(pm.request.method).to.eql('GET');
});

// Store first handbag ID for subsequent tests
if (pm.response.code === 200) {
    const responseJson = pm.response.json();
    if (responseJson.length > 0) {
        pm.environment.set('existing_handbag_id', responseJson[0].handbagId || responseJson[0].id);
    }
}
*/

// ========================================
// 6B. GET HANDBAG BY ID TEST SCRIPT
// ========================================
/*
// Test 6b: Get Handbag by ID
pm.test('Status code is 200', function () {
    pm.response.to.have.status(200);
});

pm.test('Response is a single handbag object', function () {
    const responseJson = pm.response.json();
    pm.expect(responseJson).to.be.an('object');
    pm.expect(responseJson).to.have.property('handbagId').or.to.have.property('id');
});

pm.test('JWT token was included in request', function () {
    pm.expect(pm.request.headers.get('Authorization')).to.include('Bearer');
});

pm.test('Request method is GET', function () {
    pm.expect(pm.request.method).to.eql('GET');
});
*/

// ========================================
// UNAUTHORIZED ACCESS TEST SCRIPT
// ========================================
/*
// Test: Unauthorized Access
pm.test('Status code is 401 (Unauthorized)', function () {
    pm.response.to.have.status(401);
});

pm.test('No Authorization header in request', function () {
    pm.expect(pm.request.headers.get('Authorization')).to.be.undefined;
});
*/

// ========================================
// FORBIDDEN ACCESS TEST SCRIPT (403)
// ========================================
/*
// Test: Forbidden Access
pm.test('Status code is 403 (Forbidden)', function () {
    pm.response.to.have.status(403);
});

pm.test('Response indicates permission denied', function () {
    pm.expect(pm.response.code).to.eql(403);
});

pm.test('JWT token was included in request', function () {
    pm.expect(pm.request.headers.get('Authorization')).to.include('Bearer');
});
*/

// ========================================
// QUICK ENVIRONMENT SETUP SCRIPT
// ========================================
/*
// Add this to Pre-request Script tab for quick setup
// Set default base URL if not set
if (!pm.environment.get('base_url')) {
    pm.environment.set('base_url', 'https://localhost:7176');
}

// Set default test IDs if not set
if (!pm.environment.get('handbag_id_to_update')) {
    pm.environment.set('handbag_id_to_update', '1');
}
if (!pm.environment.get('handbag_id_to_delete')) {
    pm.environment.set('handbag_id_to_delete', '2');
}
if (!pm.environment.get('existing_handbag_id')) {
    pm.environment.set('existing_handbag_id', '1');
}
*/

// ========================================
// QUICK URL SWITCHER
// ========================================
/*
// Add this to switch between HTTP and HTTPS quickly
// Uncomment the line you want to use:

// For HTTPS (default):
// pm.environment.set('base_url', 'https://localhost:7176');

// For HTTP:
// pm.environment.set('base_url', 'http://localhost:5038');

// For IIS Express:
// pm.environment.set('base_url', 'http://localhost:4381');
*/
