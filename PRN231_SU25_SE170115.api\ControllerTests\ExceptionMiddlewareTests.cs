using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Moq;
using Middleware;
using System.Text.Json;

namespace ControllerTests;

public class ExceptionMiddlewareTests
{
    private readonly Mock<ILogger<ExceptionMiddleware>> _mockLogger;
    private readonly ExceptionMiddleware _middleware;
    private readonly Mock<RequestDelegate> _mockNext;

    public ExceptionMiddlewareTests()
    {
        _mockLogger = new Mock<ILogger<ExceptionMiddleware>>();
        _mockNext = new Mock<RequestDelegate>();
        _middleware = new ExceptionMiddleware(_mockNext.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task InvokeAsync_NoException_CallsNext()
    {
        // Arrange
        var context = new DefaultHttpContext();
        _mockNext.Setup(n => n(context)).Returns(Task.CompletedTask);

        // Act
        await _middleware.InvokeAsync(context);

        // Assert
        _mockNext.Verify(n => n(context), Times.Once);
    }

    [Fact]
    public async Task InvokeAsync_FormatException_Returns400WithCorrectErrorCode()
    {
        // Arrange
        var context = new DefaultHttpContext();
        context.Response.Body = new MemoryStream();
        
        _mockNext.Setup(n => n(context)).ThrowsAsync(new FormatException("Test format exception"));

        // Act
        await _middleware.InvokeAsync(context);

        // Assert
        Assert.Equal(400, context.Response.StatusCode);
        Assert.Equal("application/json", context.Response.ContentType);

        context.Response.Body.Seek(0, SeekOrigin.Begin);
        var responseBody = await new StreamReader(context.Response.Body).ReadToEndAsync();
        var response = JsonSerializer.Deserialize<Dictionary<string, string>>(responseBody);
        
        Assert.Equal("HB40001", response["errorCode"]);
        Assert.Equal("Missing/invalid input", response["message"]);
    }

    [Fact]
    public async Task InvokeAsync_UnauthorizedTokenException_Returns401WithCorrectErrorCode()
    {
        // Arrange
        var context = new DefaultHttpContext();
        context.Response.Body = new MemoryStream();
        
        _mockNext.Setup(n => n(context)).ThrowsAsync(new UnauthorizedTokenException("Test unauthorized token"));

        // Act
        await _middleware.InvokeAsync(context);

        // Assert
        Assert.Equal(401, context.Response.StatusCode);
        Assert.Equal("application/json", context.Response.ContentType);

        context.Response.Body.Seek(0, SeekOrigin.Begin);
        var responseBody = await new StreamReader(context.Response.Body).ReadToEndAsync();
        var response = JsonSerializer.Deserialize<Dictionary<string, string>>(responseBody);
        
        Assert.Equal("HB40101", response["errorCode"]);
        Assert.Equal("Token missing/invalid", response["message"]);
    }

    [Fact]
    public async Task InvokeAsync_UnauthorizedAccessException_Returns403WithCorrectErrorCode()
    {
        // Arrange
        var context = new DefaultHttpContext();
        context.Response.Body = new MemoryStream();
        
        _mockNext.Setup(n => n(context)).ThrowsAsync(new UnauthorizedAccessException("Test unauthorized access"));

        // Act
        await _middleware.InvokeAsync(context);

        // Assert
        Assert.Equal(403, context.Response.StatusCode);
        Assert.Equal("application/json", context.Response.ContentType);

        context.Response.Body.Seek(0, SeekOrigin.Begin);
        var responseBody = await new StreamReader(context.Response.Body).ReadToEndAsync();
        var response = JsonSerializer.Deserialize<Dictionary<string, string>>(responseBody);
        
        Assert.Equal("HB40301", response["errorCode"]);
        Assert.Equal("Permission denied", response["message"]);
    }

    [Fact]
    public async Task InvokeAsync_KeyNotFoundException_Returns404WithCorrectErrorCode()
    {
        // Arrange
        var context = new DefaultHttpContext();
        context.Response.Body = new MemoryStream();
        
        _mockNext.Setup(n => n(context)).ThrowsAsync(new KeyNotFoundException("Test key not found"));

        // Act
        await _middleware.InvokeAsync(context);

        // Assert
        Assert.Equal(404, context.Response.StatusCode);
        Assert.Equal("application/json", context.Response.ContentType);

        context.Response.Body.Seek(0, SeekOrigin.Begin);
        var responseBody = await new StreamReader(context.Response.Body).ReadToEndAsync();
        var response = JsonSerializer.Deserialize<Dictionary<string, string>>(responseBody);
        
        Assert.Equal("HB40401", response["errorCode"]);
        Assert.Equal("Resource not found", response["message"]);
    }

    [Fact]
    public async Task InvokeAsync_CustomsNotFoundException_Returns404WithCorrectErrorCode()
    {
        // Arrange
        var context = new DefaultHttpContext();
        context.Response.Body = new MemoryStream();
        
        _mockNext.Setup(n => n(context)).ThrowsAsync(new CustomsNotFoundException("Test customs not found"));

        // Act
        await _middleware.InvokeAsync(context);

        // Assert
        Assert.Equal(404, context.Response.StatusCode);
        Assert.Equal("application/json", context.Response.ContentType);

        context.Response.Body.Seek(0, SeekOrigin.Begin);
        var responseBody = await new StreamReader(context.Response.Body).ReadToEndAsync();
        var response = JsonSerializer.Deserialize<Dictionary<string, string>>(responseBody);
        
        Assert.Equal("HB40401", response["errorCode"]);
        Assert.Equal("Resource not found", response["message"]);
    }

    [Fact]
    public async Task InvokeAsync_GenericException_Returns500WithCorrectErrorCode()
    {
        // Arrange
        var context = new DefaultHttpContext();
        context.Response.Body = new MemoryStream();
        
        _mockNext.Setup(n => n(context)).ThrowsAsync(new Exception("Test generic exception"));

        // Act
        await _middleware.InvokeAsync(context);

        // Assert
        Assert.Equal(500, context.Response.StatusCode);
        Assert.Equal("application/json", context.Response.ContentType);

        context.Response.Body.Seek(0, SeekOrigin.Begin);
        var responseBody = await new StreamReader(context.Response.Body).ReadToEndAsync();
        var response = JsonSerializer.Deserialize<Dictionary<string, string>>(responseBody);
        
        Assert.Equal("HB50001", response["errorCode"]);
        Assert.Equal("Internal server error", response["message"]);
    }

    [Fact]
    public async Task InvokeAsync_LogsException()
    {
        // Arrange
        var context = new DefaultHttpContext();
        context.Response.Body = new MemoryStream();
        var testException = new Exception("Test exception");
        
        _mockNext.Setup(n => n(context)).ThrowsAsync(testException);

        // Act
        await _middleware.InvokeAsync(context);

        // Assert
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Exception occurred: Test exception")),
                testException,
                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task InvokeAsync_ResponseFormatIsCorrect()
    {
        // Arrange
        var context = new DefaultHttpContext();
        context.Response.Body = new MemoryStream();
        
        _mockNext.Setup(n => n(context)).ThrowsAsync(new FormatException("Test"));

        // Act
        await _middleware.InvokeAsync(context);

        // Assert
        context.Response.Body.Seek(0, SeekOrigin.Begin);
        var responseBody = await new StreamReader(context.Response.Body).ReadToEndAsync();
        
        // Verify JSON structure matches exactly: {"errorCode": "...", "message": "..."}
        var response = JsonSerializer.Deserialize<Dictionary<string, string>>(responseBody);
        Assert.Equal(2, response.Count);
        Assert.True(response.ContainsKey("errorCode"));
        Assert.True(response.ContainsKey("message"));
        Assert.Equal("HB40001", response["errorCode"]);
        Assert.Equal("Missing/invalid input", response["message"]);
    }
}
