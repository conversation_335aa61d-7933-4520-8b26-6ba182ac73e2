{"id": "sample-environment-id", "name": "Sample API Environment - Template", "values": [{"key": "base_url", "value": "https://localhost:7176", "type": "default", "enabled": true}, {"key": "project_name", "value": "My API Project Tests", "type": "default", "enabled": true}, {"key": "api_path", "value": "api", "type": "default", "enabled": true}, {"key": "resource_name", "value": "items", "type": "default", "enabled": true}, {"key": "auth_endpoint", "value": "auth", "type": "default", "enabled": true}, {"key": "admin_email", "value": "<EMAIL>", "type": "default", "enabled": true}, {"key": "admin_password", "value": "admin123", "type": "default", "enabled": true}, {"key": "member_email", "value": "<EMAIL>", "type": "default", "enabled": true}, {"key": "member_password", "value": "user123", "type": "default", "enabled": true}, {"key": "invalid_email", "value": "<EMAIL>", "type": "default", "enabled": true}, {"key": "invalid_password", "value": "wrongpassword", "type": "default", "enabled": true}, {"key": "item_id_to_update", "value": "1", "type": "default", "enabled": true}, {"key": "item_id_to_delete", "value": "2", "type": "default", "enabled": true}, {"key": "existing_item_id", "value": "1", "type": "default", "enabled": true}, {"key": "create_item_body", "value": "{\n  \"name\": \"Test Item\",\n  \"description\": \"A test item\",\n  \"price\": 99.99,\n  \"categoryId\": 1\n}", "type": "default", "enabled": true}, {"key": "update_item_body", "value": "{\n  \"name\": \"Updated Item\",\n  \"description\": \"An updated item\",\n  \"price\": 149.99,\n  \"categoryId\": 2\n}", "type": "default", "enabled": true}, {"key": "unauthorized_create_body", "value": "{\n  \"name\": \"Unauthorized Item\",\n  \"description\": \"Should not be created\",\n  \"price\": 49.99,\n  \"categoryId\": 1\n}", "type": "default", "enabled": true}, {"key": "admin_token", "value": "", "type": "secret", "enabled": true}, {"key": "member_token", "value": "", "type": "secret", "enabled": true}, {"key": "admin_role", "value": "", "type": "default", "enabled": true}, {"key": "created_item_id", "value": "", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2024-07-18T00:00:00.000Z", "_postman_exported_using": "Postman/10.0.0"}