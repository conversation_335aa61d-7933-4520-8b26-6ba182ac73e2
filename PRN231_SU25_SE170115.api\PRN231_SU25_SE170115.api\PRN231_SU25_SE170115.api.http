@PRN231_SU25_SE170115.api_HostAddress = https://localhost:7176

# ===== AUTHENTICATION TESTS =====

### 1. Login with valid credentials (admin)
POST {{PRN231_SU25_SE170115.api_HostAddress}}/api/auth
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "123456"
}

### 2. Login with invalid credentials (should return 401)
POST {{PRN231_SU25_SE170115.api_HostAddress}}/api/auth
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "wrongpassword"
}

### 3. Login with moderator credentials
POST {{PRN231_SU25_SE170115.api_HostAddress}}/api/auth
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "123456"
}

### 4. Login with developer credentials
POST {{PRN231_SU25_SE170115.api_HostAddress}}/api/auth
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "123456"
}

### 5. <PERSON>gin with member credentials
POST {{PRN231_SU25_SE170115.api_HostAddress}}/api/auth
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "123456"
}

# ===== HANDBAG API TESTS =====
# Note: Replace <JWT_TOKEN> with actual token from login response

### 6. GET /api/handbags - List all handbags (with admin token)
GET {{PRN231_SU25_SE170115.api_HostAddress}}/api/handbags
Authorization: Bearer <JWT_TOKEN>
Accept: application/json

### 7. GET /api/handbags - Without token (should return 401)
GET {{PRN231_SU25_SE170115.api_HostAddress}}/api/handbags
Accept: application/json

### 8. GET /api/handbags/{id} - Get handbag by valid ID
GET {{PRN231_SU25_SE170115.api_HostAddress}}/api/handbags/1
Authorization: Bearer <JWT_TOKEN>
Accept: application/json

### 9. GET /api/handbags/{id} - Get handbag by invalid ID (should return 404)
GET {{PRN231_SU25_SE170115.api_HostAddress}}/api/handbags/999
Authorization: Bearer <JWT_TOKEN>
Accept: application/json

### 10. POST /api/handbags - Create handbag with valid data (admin/moderator only)
POST {{PRN231_SU25_SE170115.api_HostAddress}}/api/handbags
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "modelName": "Elegant #2024",
  "material": "Leather",
  "price": 250.5,
  "stock": 10,
  "brandId": 1
}

### 11. POST /api/handbags - Create handbag with invalid modelName (should return 400)
POST {{PRN231_SU25_SE170115.api_HostAddress}}/api/handbags
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "modelName": "invalid-name",
  "material": "Leather",
  "price": 250.5,
  "stock": 10,
  "brandId": 1
}

### 12. POST /api/handbags - Create handbag with invalid price (should return 400)
POST {{PRN231_SU25_SE170115.api_HostAddress}}/api/handbags
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "modelName": "Valid #Name",
  "material": "Leather",
  "price": -10,
  "stock": 10,
  "brandId": 1
}

### 13. POST /api/handbags - Create handbag with invalid stock (should return 400)
POST {{PRN231_SU25_SE170115.api_HostAddress}}/api/handbags
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "modelName": "Valid #Name",
  "material": "Leather",
  "price": 250.5,
  "stock": 0,
  "brandId": 1
}

### 14. POST /api/handbags - Create handbag with developer token (should return 403)
POST {{PRN231_SU25_SE170115.api_HostAddress}}/api/handbags
Authorization: Bearer <DEVELOPER_JWT_TOKEN>
Content-Type: application/json

{
  "modelName": "Test #Bag",
  "material": "Leather",
  "price": 250.5,
  "stock": 10,
  "brandId": 1
}

### 15. PUT /api/handbags/{id} - Update existing handbag (admin/moderator only)
PUT {{PRN231_SU25_SE170115.api_HostAddress}}/api/handbags/1
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "modelName": "Updated #2024",
  "material": "Premium Leather",
  "price": 300.0,
  "stock": 15,
  "brandId": 1
}

### 16. PUT /api/handbags/{id} - Update non-existing handbag (should return 404)
PUT {{PRN231_SU25_SE170115.api_HostAddress}}/api/handbags/999
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "modelName": "Updated #2024",
  "material": "Premium Leather",
  "price": 300.0,
  "stock": 15,
  "brandId": 1
}

### 17. PUT /api/handbags/{id} - Update with invalid data (should return 400)
PUT {{PRN231_SU25_SE170115.api_HostAddress}}/api/handbags/1
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "modelName": "invalid-name",
  "material": "Premium Leather",
  "price": -100,
  "stock": 0,
  "brandId": 1
}

### 18. PUT /api/handbags/{id} - Update with developer token (should return 403)
PUT {{PRN231_SU25_SE170115.api_HostAddress}}/api/handbags/1
Authorization: Bearer <DEVELOPER_JWT_TOKEN>
Content-Type: application/json

{
  "modelName": "Test #Update",
  "material": "Leather",
  "price": 250.0,
  "stock": 10,
  "brandId": 1
}

### 19. DELETE /api/handbags/{id} - Delete existing handbag (admin/moderator only)
DELETE {{PRN231_SU25_SE170115.api_HostAddress}}/api/handbags/1
Authorization: Bearer <JWT_TOKEN>

### 20. DELETE /api/handbags/{id} - Delete non-existing handbag (should return 404)
DELETE {{PRN231_SU25_SE170115.api_HostAddress}}/api/handbags/999
Authorization: Bearer <JWT_TOKEN>

### 21. DELETE /api/handbags/{id} - Delete with developer token (should return 403)
DELETE {{PRN231_SU25_SE170115.api_HostAddress}}/api/handbags/2
Authorization: Bearer <DEVELOPER_JWT_TOKEN>

### 22. GET /api/handbags/search - Search with modelName parameter
GET {{PRN231_SU25_SE170115.api_HostAddress}}/api/handbags/search?modelName=Elegant
Authorization: Bearer <JWT_TOKEN>
Accept: application/json

### 23. GET /api/handbags/search - Search with material parameter
GET {{PRN231_SU25_SE170115.api_HostAddress}}/api/handbags/search?material=Leather
Authorization: Bearer <JWT_TOKEN>
Accept: application/json

### 24. GET /api/handbags/search - Search with both parameters
GET {{PRN231_SU25_SE170115.api_HostAddress}}/api/handbags/search?modelName=Elegant&material=Leather
Authorization: Bearer <JWT_TOKEN>
Accept: application/json

### 25. GET /api/handbags/search - Search without token (should return 401)
GET {{PRN231_SU25_SE170115.api_HostAddress}}/api/handbags/search?modelName=Elegant
Accept: application/json

### 26. GET /api/handbags/search - Search with member token (should work)
GET {{PRN231_SU25_SE170115.api_HostAddress}}/api/handbags/search?modelName=Elegant
Authorization: Bearer <MEMBER_JWT_TOKEN>
Accept: application/json

# ===== ADDITIONAL VALIDATION TESTS =====

### 27. POST /api/handbags - Test valid modelName patterns
POST {{PRN231_SU25_SE170115.api_HostAddress}}/api/handbags
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "modelName": "A1",
  "material": "Leather",
  "price": 100.0,
  "stock": 5,
  "brandId": 1
}

### 28. POST /api/handbags - Test valid modelName with numbers and hash
POST {{PRN231_SU25_SE170115.api_HostAddress}}/api/handbags
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "modelName": "Model #123 Test",
  "material": "Canvas",
  "price": 150.0,
  "stock": 8,
  "brandId": 2
}

### 29. POST /api/handbags - Test invalid modelName (starts with lowercase)
POST {{PRN231_SU25_SE170115.api_HostAddress}}/api/handbags
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "modelName": "invalid Model",
  "material": "Leather",
  "price": 200.0,
  "stock": 10,
  "brandId": 1
}

### 30. POST /api/handbags - Test missing required fields
POST {{PRN231_SU25_SE170115.api_HostAddress}}/api/handbags
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "material": "Leather",
  "price": 200.0,
  "stock": 10
}

# ===== ROLE-BASED ACCESS TESTS =====

### 31. GET /api/handbags - Test with member token (should work)
GET {{PRN231_SU25_SE170115.api_HostAddress}}/api/handbags
Authorization: Bearer <MEMBER_JWT_TOKEN>
Accept: application/json

### 32. GET /api/handbags - Test with developer token (should work)
GET {{PRN231_SU25_SE170115.api_HostAddress}}/api/handbags
Authorization: Bearer <DEVELOPER_JWT_TOKEN>
Accept: application/json

### 33. POST /api/handbags - Test with member token (should return 403)
POST {{PRN231_SU25_SE170115.api_HostAddress}}/api/handbags
Authorization: Bearer <MEMBER_JWT_TOKEN>
Content-Type: application/json

{
  "modelName": "Test #Bag",
  "material": "Leather",
  "price": 250.5,
  "stock": 10,
  "brandId": 1
}

###
