# Flexible Postman Collection Usage Guide

## Overview
This Postman collection has been designed to be highly flexible and reusable across different API projects. You only need to modify environment variables or request bodies to adapt it to your specific project.

## Quick Setup for New Projects

### 1. Environment Variables to Change

#### Basic Configuration
- `project_name`: Name of your project (appears in collection title)
- `base_url`: Your API base URL (e.g., `https://localhost:7176`)
- `api_path`: API path prefix (e.g., `api`, `v1`, `api/v1`)
- `resource_name`: Main resource name (e.g., `handbags`, `products`, `users`)
- `auth_endpoint`: Authentication endpoint (e.g., `auth`, `login`, `authenticate`)

#### Authentication Credentials
- `admin_email`: Administrator email for login
- `admin_password`: Administrator password
- `member_email`: Member/user email for login
- `member_password`: Member/user password
- `invalid_email`: Invalid email for failed login test
- `invalid_password`: Invalid password for failed login test

#### Test Data IDs
- `item_id_to_update`: ID of item to update in tests
- `item_id_to_delete`: ID of item to delete in tests
- `existing_item_id`: ID of existing item for GET by ID test

#### Request Bodies (JSON format)
- `create_item_body`: JSON body for creating new items
- `update_item_body`: JSON body for updating items
- `unauthorized_create_body`: JSON body for unauthorized create test

### 2. Example: Adapting for a "Products" API

To use this collection for a Products API instead of Handbags:

```json
{
  "project_name": "Products API Tests - MyProject",
  "base_url": "https://api.myproject.com",
  "api_path": "api/v1",
  "resource_name": "products",
  "auth_endpoint": "login",
  
  "admin_email": "<EMAIL>",
  "admin_password": "admin123",
  "member_email": "<EMAIL>",
  "member_password": "user123",
  
  "create_item_body": "{\n  \"name\": \"Test Product\",\n  \"description\": \"A test product\",\n  \"price\": 99.99,\n  \"categoryId\": 1\n}",
  
  "update_item_body": "{\n  \"name\": \"Updated Product\",\n  \"description\": \"An updated product\",\n  \"price\": 149.99,\n  \"categoryId\": 2\n}"
}
```

### 3. Example: Adapting for a "Books" API

```json
{
  "project_name": "Books API Tests - Library System",
  "base_url": "https://localhost:5000",
  "api_path": "api",
  "resource_name": "books",
  "auth_endpoint": "authenticate",
  
  "create_item_body": "{\n  \"title\": \"Test Book\",\n  \"author\": \"Test Author\",\n  \"isbn\": \"1234567890\",\n  \"publishYear\": 2024\n}",
  
  "update_item_body": "{\n  \"title\": \"Updated Book Title\",\n  \"author\": \"Updated Author\",\n  \"isbn\": \"0987654321\",\n  \"publishYear\": 2024\n}"
}
```

## How to Use

### Method 1: Environment Variables (Recommended)
1. Create a new Postman environment
2. Set the variables listed above with your project-specific values
3. Select the environment before running the collection
4. Run the tests - they will automatically use your environment values

### Method 2: Collection Variables
1. Edit the collection
2. Go to the "Variables" tab
3. Update the default values with your project-specific values
4. Save the collection

### Method 3: Manual Override
For quick testing, you can manually edit individual request bodies and URLs, but this is not recommended for reusability.

## Test Structure

The collection includes these test scenarios:
1. **Login Success** - Tests successful authentication with admin credentials
2. **Login Failed** - Tests failed authentication with invalid credentials
3. **Create Item** - Tests creating a new resource (requires admin role)
4. **Update Item** - Tests updating an existing resource (requires admin role)
5. **Delete Item** - Tests deleting a resource (requires admin role)
6. **Get All Items** - Tests retrieving all resources
7. **Get Item by ID** - Tests retrieving a specific resource by ID
8. **Bonus: Member Login** - Tests login with member/user credentials
9. **Bonus: Unauthorized Create** - Tests that members cannot create resources (should return 403)
10. **Bonus: No Token Access** - Tests that requests without tokens are rejected (should return 401)

## Automatic Features

The collection automatically:
- Stores JWT tokens from login responses
- Uses stored tokens in subsequent requests
- Extracts and stores item IDs from responses for use in other tests
- Sets default values if environment variables are not provided
- Validates response formats and status codes
- Tests JWT token format validity

## Tips for Different Projects

### For APIs with different authentication response format:
Modify the test scripts in the login requests to extract tokens from your specific response format.

### For APIs with different field names:
Update the request bodies in the environment variables to match your API's expected field names.

### For APIs with different endpoints:
Update the `api_path`, `resource_name`, and `auth_endpoint` variables to match your API structure.

### For APIs requiring additional headers:
Add them to the collection's pre-request scripts or individual request headers.

## Troubleshooting

1. **Tests failing with 404**: Check that `api_path` and `resource_name` match your API structure
2. **Authentication failing**: Verify `admin_email`, `admin_password`, and `auth_endpoint` are correct
3. **Create/Update failing**: Ensure `create_item_body` and `update_item_body` match your API's expected format
4. **Token not being stored**: Check that your login response includes a `token` field, or modify the test script accordingly

This flexible design allows you to reuse the same comprehensive test suite across multiple API projects with minimal configuration changes.
