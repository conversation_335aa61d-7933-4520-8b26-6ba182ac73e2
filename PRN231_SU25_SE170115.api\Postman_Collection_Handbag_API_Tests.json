{"info": {"_postman_id": "handbag-api-tests-collection", "name": "Handbag API Tests - PRN231_SU25_SE170115", "description": "Comprehensive test collection for Handbag API with JWT authentication", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "1. <PERSON><PERSON> (Administrator)", "event": [{"listen": "test", "script": {"exec": ["// Test 1: <PERSON><PERSON>", "pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has token and role', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('token');", "    pm.expect(responseJson).to.have.property('role');", "    pm.expect(responseJson.role).to.eql('administrator');", "});", "", "pm.test('Token is valid JWT format', function () {", "    const responseJson = pm.response.json();", "    const token = responseJson.token;", "    pm.expect(token).to.match(/^[A-Za-z0-9-_]+\\.[A-Za-z0-9-_]+\\.[A-Za-z0-9-_]+$/);", "});", "", "// Store token for subsequent requests", "if (pm.response.code === 200) {", "    const responseJson = pm.response.json();", "    pm.environment.set('admin_token', responseJson.token);", "    pm.environment.set('admin_role', responseJson.role);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"123456\"\n}"}, "url": {"raw": "{{base_url}}/api/auth", "host": ["{{base_url}}"], "path": ["api", "auth"]}}}, {"name": "2. <PERSON><PERSON> (Invalid Credentials)", "event": [{"listen": "test", "script": {"exec": ["// Test 2: <PERSON><PERSON> Failed", "pm.test('Status code is 500 (Internal Server Error due to exception handling)', function () {", "    pm.response.to.have.status(500);", "});", "", "pm.test('Response indicates error', function () {", "    // The API throws InternalServerErrorException for failed login", "    pm.expect(pm.response.code).to.be.oneOf([404, 500]);", "});", "", "pm.test('No token in response', function () {", "    try {", "        const responseJson = pm.response.json();", "        pm.expect(response<PERSON><PERSON>).to.not.have.property('token');", "    } catch (e) {", "        // Response might not be JSON for error cases", "        pm.expect(true).to.be.true;", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"wrongpassword\"\n}"}, "url": {"raw": "{{base_url}}/api/auth", "host": ["{{base_url}}"], "path": ["api", "auth"]}}}, {"name": "3. <PERSON><PERSON> (Administrator - Authorized)", "event": [{"listen": "test", "script": {"exec": ["// Test 3: Create Handbag Success", "pm.test('Status code is 200 or 201', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201]);", "});", "", "pm.test('Response contains success message or created handbag', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.be.an('object');", "    // Store created handbag ID if available", "    if (responseJson.handbagId || responseJson.id) {", "        pm.environment.set('created_handbag_id', responseJson.handbagId || responseJson.id);", "    }", "});", "", "pm.test('JWT token was included in request', function () {", "    pm.expect(pm.request.headers.get('Authorization')).to.include('Bearer');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{admin_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"modelName\": \"Test Luxury #Bag\",\n  \"material\": \"Leather\",\n  \"price\": 299.99,\n  \"stock\": 15,\n  \"brandId\": 1\n}"}, "url": {"raw": "{{base_url}}/api/handbags", "host": ["{{base_url}}"], "path": ["api", "handbags"]}}}, {"name": "4. <PERSON><PERSON> <PERSON><PERSON> (Administrator - Authorized)", "event": [{"listen": "test", "script": {"exec": ["// Test 4: Update Handbag Success", "pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains success message or updated handbag', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.be.an('object');", "});", "", "pm.test('JWT token was included in request', function () {", "    pm.expect(pm.request.headers.get('Authorization')).to.include('Bearer');", "});", "", "pm.test('Request method is PUT', function () {", "    pm.expect(pm.request.method).to.eql('PUT');", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{admin_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"modelName\": \"Updated Luxury #Bag\",\n  \"material\": \"Premium Leather\",\n  \"color\": \"Black\",\n  \"price\": 349.99,\n  \"stock\": 20,\n  \"brandId\": 1\n}"}, "url": {"raw": "{{base_url}}/api/handbags/{{handbag_id_to_update}}", "host": ["{{base_url}}"], "path": ["api", "handbags", "{{handbag_id_to_update}}"]}}}, {"name": "5. <PERSON><PERSON> (Administrator - Authorized)", "event": [{"listen": "test", "script": {"exec": ["// Test 5: Delete Handbag Success", "pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains success message', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('Deleted successfully');", "});", "", "pm.test('JWT token was included in request', function () {", "    pm.expect(pm.request.headers.get('Authorization')).to.include('Bearer');", "});", "", "pm.test('Request method is DELETE', function () {", "    pm.expect(pm.request.method).to.eql('DELETE');", "});"], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/handbags/{{handbag_id_to_delete}}", "host": ["{{base_url}}"], "path": ["api", "handbags", "{{handbag_id_to_delete}}"]}}}, {"name": "6a. Get All Handbags (List)", "event": [{"listen": "test", "script": {"exec": ["// Test 6a: Get All Handbags", "pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response is an array of handbags', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.be.an('array');", "});", "", "pm.test('JWT token was included in request', function () {", "    pm.expect(pm.request.headers.get('Authorization')).to.include('Bearer');", "});", "", "pm.test('Request method is GET', function () {", "    pm.expect(pm.request.method).to.eql('GET');", "});", "", "// Store first handbag ID for subsequent tests", "if (pm.response.code === 200) {", "    const responseJson = pm.response.json();", "    if (responseJson.length > 0) {", "        pm.environment.set('existing_handbag_id', responseJson[0].handbagId || responseJson[0].id);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/handbags", "host": ["{{base_url}}"], "path": ["api", "handbags"]}}}, {"name": "6b. <PERSON> Handbag by ID", "event": [{"listen": "test", "script": {"exec": ["// Test 6b: Get Handbag by ID", "pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response is a single handbag object', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.be.an('object');", "    pm.expect(response<PERSON><PERSON>).to.have.property('handbagId').or.to.have.property('id');", "});", "", "pm.test('JWT token was included in request', function () {", "    pm.expect(pm.request.headers.get('Authorization')).to.include('Bearer');", "});", "", "pm.test('Request method is GET', function () {", "    pm.expect(pm.request.method).to.eql('GET');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/handbags/{{existing_handbag_id}}", "host": ["{{base_url}}"], "path": ["api", "handbags", "{{existing_handbag_id}}"]}}}, {"name": "BONUS: <PERSON><PERSON> as Member (Limited Access)", "event": [{"listen": "test", "script": {"exec": ["// Bonus Test: <PERSON><PERSON> as Member", "pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has token and member role', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('token');", "    pm.expect(responseJson).to.have.property('role');", "    pm.expect(responseJson.role).to.eql('member');", "});", "", "// Store member token for subsequent tests", "if (pm.response.code === 200) {", "    const responseJson = pm.response.json();", "    pm.environment.set('member_token', responseJson.token);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"123456\"\n}"}, "url": {"raw": "{{base_url}}/api/auth", "host": ["{{base_url}}"], "path": ["api", "auth"]}}}, {"name": "BONUS: Create Handbag with Member Token (Should Fail - 403)", "event": [{"listen": "test", "script": {"exec": ["// Bonus Test: Unauthorized Create", "pm.test('Status code is 403 (Forbidden)', function () {", "    pm.response.to.have.status(403);", "});", "", "pm.test('Response indicates permission denied', function () {", "    // The API should return 403 for unauthorized access", "    pm.expect(pm.response.code).to.eql(403);", "});", "", "pm.test('JWT token was included in request', function () {", "    pm.expect(pm.request.headers.get('Authorization')).to.include('Bearer');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{member_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"modelName\": \"Unauthorized #Bag\",\n  \"material\": \"Leather\",\n  \"price\": 199.99,\n  \"stock\": 5,\n  \"brandId\": 1\n}"}, "url": {"raw": "{{base_url}}/api/handbags", "host": ["{{base_url}}"], "path": ["api", "handbags"]}}}, {"name": "BONUS: Get Handbags without Token (Should Fail - 401)", "event": [{"listen": "test", "script": {"exec": ["// Bonus Test: Unauthorized Access", "pm.test('Status code is 401 (Unauthorized)', function () {", "    pm.response.to.have.status(401);", "});", "", "pm.test('No Authorization header in request', function () {", "    pm.expect(pm.request.headers.get('Authorization')).to.be.undefined;", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/handbags", "host": ["{{base_url}}"], "path": ["api", "handbags"]}}}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set default base URL if not set", "if (!pm.environment.get('base_url')) {", "    pm.environment.set('base_url', 'https://localhost:7176');", "}", "", "// Set default test IDs if not set", "if (!pm.environment.get('handbag_id_to_update')) {", "    pm.environment.set('handbag_id_to_update', '1');", "}", "if (!pm.environment.get('handbag_id_to_delete')) {", "    pm.environment.set('handbag_id_to_delete', '2');", "}", "if (!pm.environment.get('existing_handbag_id')) {", "    pm.environment.set('existing_handbag_id', '1');", "}"]}}], "variable": [{"key": "base_url", "value": "https://localhost:7176", "type": "string"}]}