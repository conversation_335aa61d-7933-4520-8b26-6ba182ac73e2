﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OData.Query;
using Model;
using Services;
using Middleware;
using System.Text.RegularExpressions;
using Microsoft.AspNetCore.OData.Query.Validator;

namespace PRN231_SU25_SE170115.api.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class HandbagsController : ControllerBase
    {
        private readonly HangBagService _handbagService;

        public HandbagsController(HangBagService handbagService)
        {
            _handbagService = handbagService;
        }

        [HttpGet]
        [Authorize(Roles = "administrator,moderator,developer,member")]
        public IActionResult GetAllHandbags()
        {
            try
            {
                var handbags = _handbagService.GetAllHandBag();
                return Ok(handbags);
            }
            catch (Exception e)
            {
                throw new InternalServerErrorException();
            }
        }

        [HttpGet("{id}")]
        [Authorize(Roles = "administrator,moderator,developer,member")]
        public IActionResult GetHandbagById(int id)
        {
            try
            {
                var handbag = _handbagService.GetHandBagById(id);
                if (handbag == null)
                    throw new NotFoundException();

                return Ok(handbag);
            }
            catch (Exception e)
            {
                throw new InternalServerErrorException();
            }
        }

        [HttpPost]
        [Authorize(Roles = "administrator,moderator")]
        public IActionResult CreateHandbag([FromBody] CreateHandBagModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                    throw new BadRequestException();

                // Check if user has permission (developer and member cannot create)
                var userRole = User.FindFirst("http://schemas.microsoft.com/ws/2008/06/identity/claims/role")?.Value;
                if (userRole == "developer" || userRole == "member")
                {
                    return StatusCode(403, new UnauthorizedAccessException("Permission denied"));
                }

                var regex = new Regex(@"^([A-Z0-9][a-zA-Z0-9#]*\s)*([A-Z0-9][a-zA-Z0-9#]*)$");
                if (!regex.IsMatch(model.ModelName) || model.Price <= 0 || model.Stock <= 0)
                    throw new BadRequestException();

                var result = _handbagService.CreateHandBag(model);
                if (!result)
                {
                    throw new BadRequestException();
                }

                return StatusCode(201, new { message = "Created successfully" });
            }
            catch (Exception e)
            {
                throw new InternalServerErrorException();
            }
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "administrator,moderator")]
        public IActionResult UpdateHandbag(int id, [FromBody] UpdateHandBagModel model)
        {
            try
            {
                // Check if user has permission (developer and member cannot update)
                var userRole = User.FindFirst("http://schemas.microsoft.com/ws/2008/06/identity/claims/role")?.Value;
                if (userRole == "developer" || userRole == "member")
                {
                    return StatusCode(403, new UnauthorizedAccessException("Permission denied"));
                }

                // Validate ID
                if (id <= 0)
                {
                    throw new BadRequestException();
                }

                var item = _handbagService.GetHandBagById(id);
                if (item == null)
                {
                    throw new NotFoundException();
                }

                if (!ModelState.IsValid)
                    throw new BadRequestException();


                var regex = new Regex(@"^([A-Z0-9][a-zA-Z0-9#]*\s)*([A-Z0-9][a-zA-Z0-9#]*)$");
                if (!regex.IsMatch(model.ModelName) || model.Price <= 0 || model.Stock <= 0)
                    throw new BadRequestException();

                var result = _handbagService.UpdateHandBag(id, model);
                if (!result)
                {
                    throw new BadRequestException();
                }

                return Ok(new { message = "Updated successfully" });
            }
            catch (Exception e)
            {
                throw new InternalServerErrorException();
            }
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "administrator,moderator")]
        public IActionResult DeleteHandbag(int id)
        {
            try
            {
                // Check if user has permission (developer and member cannot delete)
                var userRole = User.FindFirst("http://schemas.microsoft.com/ws/2008/06/identity/claims/role")?.Value;
                if (userRole == "developer" || userRole == "member")
                {
                    return StatusCode(403, new UnauthorizedAccessException("Permission denied"));
                }

                // Validate ID
                if (id <= 0)
                {
                    throw new BadRequestException();
                }

                var result = _handbagService.DeleteHandBag(id);
                if (!result)
                {
                    throw new NotFoundException();
                }

                return Ok(new { message = "Deleted successfully" });
            }
            catch (Exception e)
            {
                throw new InternalServerErrorException();
            }
        }

        [Authorize(Roles = "administrator,moderator,developer,member")]
        [HttpGet("search")]
        public IActionResult SearchHandbags([FromServices] ODataQueryOptions<ListHandBagModel> odataOptions, [FromQuery] string? modelName, [FromQuery] string? material)
        {
            var query = _handbagService.SearchWithProjection(modelName, material);

            // ✳️ Áp dụng OData thủ công lên IQueryable trước khi materialize
            var settings = new ODataValidationSettings();
            try
            {
                odataOptions.Validate(settings);

                var results = (IQueryable<ListHandBagModel>)odataOptions.ApplyTo(query);

                var filtered = results.ToList(); // materialize sau khi OData xử lý

                // ✅ GroupBy sau khi đã xử lý OData xong
                var grouped = filtered
                    .GroupBy(h => h.BrandName)
                    .Select(g => new GroupedHandbagModel
                    {
                        BrandName = g.Key,
                        Handbags = g.ToList()
                    });
                return Ok(grouped);
            }
            catch (Exception e)
            {
                throw new InternalServerErrorException();
            }

        }
    }
}
