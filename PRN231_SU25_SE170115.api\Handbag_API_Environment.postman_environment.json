{"id": "handbag-api-environment-id", "name": "Handbag API - PRN231_SU25_SE170115", "values": [{"key": "base_url", "value": "https://localhost:7176", "type": "default", "enabled": true}, {"key": "project_name", "value": "Handbag API Tests - PRN231_SU25_SE170115", "type": "default", "enabled": true}, {"key": "api_path", "value": "api", "type": "default", "enabled": true}, {"key": "resource_name", "value": "handbags", "type": "default", "enabled": true}, {"key": "auth_endpoint", "value": "auth", "type": "default", "enabled": true}, {"key": "admin_email", "value": "<EMAIL>", "type": "default", "enabled": true}, {"key": "admin_password", "value": "123456", "type": "default", "enabled": true}, {"key": "member_email", "value": "<EMAIL>", "type": "default", "enabled": true}, {"key": "member_password", "value": "123456", "type": "default", "enabled": true}, {"key": "invalid_email", "value": "<EMAIL>", "type": "default", "enabled": true}, {"key": "invalid_password", "value": "wrongpassword", "type": "default", "enabled": true}, {"key": "item_id_to_update", "value": "1", "type": "default", "enabled": true}, {"key": "item_id_to_delete", "value": "2", "type": "default", "enabled": true}, {"key": "existing_item_id", "value": "1", "type": "default", "enabled": true}, {"key": "create_item_body", "value": "{\n  \"modelName\": \"Test Luxury #Bag\",\n  \"material\": \"Leather\",\n  \"price\": 299.99,\n  \"stock\": 15,\n  \"brandId\": 1\n}", "type": "default", "enabled": true}, {"key": "update_item_body", "value": "{\n  \"modelName\": \"Updated Luxury #Bag\",\n  \"material\": \"Premium Leather\",\n  \"color\": \"Black\",\n  \"price\": 349.99,\n  \"stock\": 20,\n  \"brandId\": 1\n}", "type": "default", "enabled": true}, {"key": "unauthorized_create_body", "value": "{\n  \"modelName\": \"Unauthorized #Bag\",\n  \"material\": \"Leather\",\n  \"price\": 199.99,\n  \"stock\": 5,\n  \"brandId\": 1\n}", "type": "default", "enabled": true}, {"key": "admin_token", "value": "", "type": "secret", "enabled": true}, {"key": "member_token", "value": "", "type": "secret", "enabled": true}, {"key": "admin_role", "value": "", "type": "default", "enabled": true}, {"key": "created_item_id", "value": "", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2024-07-18T00:00:00.000Z", "_postman_exported_using": "Postman/10.0.0"}