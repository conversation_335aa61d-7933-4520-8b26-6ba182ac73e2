using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using Moq;
using Services;
using Model;
using PRN231_SU25_SE170115.api.Controllers;
using Middleware;
using System.Security.Claims;
using Microsoft.AspNetCore.Authorization;

namespace ControllerTests;

public class HandbagsControllerTests
{
    private readonly Mock<IHangBagService> _mockHangBagService;
    private readonly HandbagsController _controller;

    public HandbagsControllerTests()
    {
        _mockHangBagService = new Mock<IHangBagService>();
        _controller = new HandbagsController(_mockHangBagService.Object);
    }

    private void SetupUserRole(string role)
    {
        var claims = new List<Claim>
        {
            new Claim("http://schemas.microsoft.com/ws/2008/06/identity/claims/role", role)
        };
        var identity = new ClaimsIdentity(claims, "TestAuthType");
        var principal = new ClaimsPrincipal(identity);
        
        _controller.ControllerContext = new ControllerContext
        {
            HttpContext = new DefaultHttpContext
            {
                User = principal
            }
        };
    }

    [Fact]
    public void GetAllHandbags_ReturnsOkWithHandbags()
    {
        // Arrange
        var handbags = new List<ListHandBagModel>
        {
            new ListHandBagModel { HandbagId = 1, ModelName = "Test Bag 1" },
            new ListHandBagModel { HandbagId = 2, ModelName = "Test Bag 2" }
        };
        _mockHangBagService.Setup(s => s.GetAllHandBag()).Returns(handbags);

        // Act
        var result = _controller.GetAllHandbags();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnedHandbags = Assert.IsType<List<ListHandBagModel>>(okResult.Value);
        Assert.Equal(2, returnedHandbags.Count);
    }

    [Fact]
    public void GetHandbagById_ValidId_ReturnsOkWithHandbag()
    {
        // Arrange
        var handbag = new ListHandBagModel { HandbagId = 1, ModelName = "Test Bag" };
        _mockHangBagService.Setup(s => s.GetHandBagById(1)).Returns(handbag);

        // Act
        var result = _controller.GetHandbagById(1);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnedHandbag = Assert.IsType<ListHandBagModel>(okResult.Value);
        Assert.Equal(1, returnedHandbag.HandbagId);
    }

    [Fact]
    public void GetHandbagById_InvalidId_ThrowsFormatException()
    {
        // Act & Assert
        var exception = Assert.Throws<FormatException>(() => _controller.GetHandbagById(0));
        Assert.Equal("Invalid handbag ID", exception.Message);
    }

    [Fact]
    public void GetHandbagById_NegativeId_ThrowsFormatException()
    {
        // Act & Assert
        var exception = Assert.Throws<FormatException>(() => _controller.GetHandbagById(-1));
        Assert.Equal("Invalid handbag ID", exception.Message);
    }

    [Fact]
    public void GetHandbagById_NotFound_ThrowsCustomsNotFoundException()
    {
        // Arrange
        _mockHangBagService.Setup(s => s.GetHandBagById(999)).Returns((ListHandBagModel)null);

        // Act & Assert
        var exception = Assert.Throws<CustomsNotFoundException>(() => _controller.GetHandbagById(999));
        Assert.Equal("Handbag not found", exception.Message);
    }

    [Fact]
    public void CreateHandbag_ValidModelAsAdministrator_ReturnsCreated()
    {
        // Arrange
        SetupUserRole("administrator");
        var model = new CreateHandBagModel 
        { 
            ModelName = "Test Bag", 
            Material = "Leather", 
            Price = 100, 
            Stock = 10, 
            BrandId = 1 
        };

        // Act
        var result = _controller.CreateHandbag(model);

        // Assert
        var statusResult = Assert.IsType<ObjectResult>(result);
        Assert.Equal(201, statusResult.StatusCode);
        _mockHangBagService.Verify(s => s.CreateHandBag(model), Times.Once);
    }

    [Fact]
    public void CreateHandbag_ValidModelAsModerator_ReturnsCreated()
    {
        // Arrange
        SetupUserRole("moderator");
        var model = new CreateHandBagModel 
        { 
            ModelName = "Test Bag", 
            Material = "Leather", 
            Price = 100, 
            Stock = 10, 
            BrandId = 1 
        };

        // Act
        var result = _controller.CreateHandbag(model);

        // Assert
        var statusResult = Assert.IsType<ObjectResult>(result);
        Assert.Equal(201, statusResult.StatusCode);
        _mockHangBagService.Verify(s => s.CreateHandBag(model), Times.Once);
    }

    [Fact]
    public void CreateHandbag_AsDeveloper_ThrowsUnauthorizedAccessException()
    {
        // Arrange
        SetupUserRole("developer");
        var model = new CreateHandBagModel 
        { 
            ModelName = "Test Bag", 
            Material = "Leather", 
            Price = 100, 
            Stock = 10, 
            BrandId = 1 
        };

        // Act & Assert
        var exception = Assert.Throws<UnauthorizedAccessException>(() => _controller.CreateHandbag(model));
        Assert.Equal("Permission denied", exception.Message);
    }

    [Fact]
    public void CreateHandbag_AsMember_ThrowsUnauthorizedAccessException()
    {
        // Arrange
        SetupUserRole("member");
        var model = new CreateHandBagModel 
        { 
            ModelName = "Test Bag", 
            Material = "Leather", 
            Price = 100, 
            Stock = 10, 
            BrandId = 1 
        };

        // Act & Assert
        var exception = Assert.Throws<UnauthorizedAccessException>(() => _controller.CreateHandbag(model));
        Assert.Equal("Permission denied", exception.Message);
    }

    [Fact]
    public void CreateHandbag_InvalidModelName_ThrowsFormatException()
    {
        // Arrange
        SetupUserRole("administrator");
        var model = new CreateHandBagModel 
        { 
            ModelName = "invalid model name", // lowercase start
            Material = "Leather", 
            Price = 100, 
            Stock = 10, 
            BrandId = 1 
        };

        // Act & Assert
        var exception = Assert.Throws<FormatException>(() => _controller.CreateHandbag(model));
        Assert.Equal("modelName format is invalid", exception.Message);
    }

    [Fact]
    public void UpdateHandbag_ValidUpdate_ReturnsOk()
    {
        // Arrange
        SetupUserRole("administrator");
        var model = new CreateHandBagModel 
        { 
            ModelName = "Updated Bag", 
            Material = "Leather", 
            Price = 150, 
            Stock = 5, 
            BrandId = 1 
        };
        _mockHangBagService.Setup(s => s.UpdateHandBag(1, model)).Returns(true);

        // Act
        var result = _controller.UpdateHandbag(1, model);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        _mockHangBagService.Verify(s => s.UpdateHandBag(1, model), Times.Once);
    }

    [Fact]
    public void UpdateHandbag_InvalidId_ThrowsFormatException()
    {
        // Arrange
        SetupUserRole("administrator");
        var model = new CreateHandBagModel 
        { 
            ModelName = "Updated Bag", 
            Material = "Leather", 
            Price = 150, 
            Stock = 5, 
            BrandId = 1 
        };

        // Act & Assert
        var exception = Assert.Throws<FormatException>(() => _controller.UpdateHandbag(0, model));
        Assert.Equal("Invalid handbag ID", exception.Message);
    }

    [Fact]
    public void UpdateHandbag_NotFound_ThrowsCustomsNotFoundException()
    {
        // Arrange
        SetupUserRole("administrator");
        var model = new CreateHandBagModel 
        { 
            ModelName = "Updated Bag", 
            Material = "Leather", 
            Price = 150, 
            Stock = 5, 
            BrandId = 1 
        };
        _mockHangBagService.Setup(s => s.UpdateHandBag(999, model)).Returns(false);

        // Act & Assert
        var exception = Assert.Throws<CustomsNotFoundException>(() => _controller.UpdateHandbag(999, model));
        Assert.Equal("Handbag not found", exception.Message);
    }

    [Fact]
    public void DeleteHandbag_ValidId_ReturnsOk()
    {
        // Arrange
        SetupUserRole("administrator");
        _mockHangBagService.Setup(s => s.DeleteHandBag(1)).Returns(true);

        // Act
        var result = _controller.DeleteHandbag(1);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        _mockHangBagService.Verify(s => s.DeleteHandBag(1), Times.Once);
    }

    [Fact]
    public void DeleteHandbag_InvalidId_ThrowsFormatException()
    {
        // Arrange
        SetupUserRole("administrator");

        // Act & Assert
        var exception = Assert.Throws<FormatException>(() => _controller.DeleteHandbag(0));
        Assert.Equal("Invalid handbag ID", exception.Message);
    }

    [Fact]
    public void DeleteHandbag_NotFound_ThrowsCustomsNotFoundException()
    {
        // Arrange
        SetupUserRole("administrator");
        _mockHangBagService.Setup(s => s.DeleteHandBag(999)).Returns(false);

        // Act & Assert
        var exception = Assert.Throws<CustomsNotFoundException>(() => _controller.DeleteHandbag(999));
        Assert.Equal("Handbag not found", exception.Message);
    }

    [Fact]
    public void SearchHandbags_ValidParameters_ReturnsOk()
    {
        // Arrange
        var handbags = new List<ListHandBagModel>
        {
            new ListHandBagModel { HandbagId = 1, ModelName = "Test Bag", BrandName = "Brand A" }
        }.AsQueryable();

        _mockHangBagService.Setup(s => s.SearchWithProjection("Test", "Leather")).Returns(handbags);

        // Act
        var result = _controller.SearchHandbags("Test", "Leather");

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.NotNull(okResult.Value);
    }

    [Fact]
    public void SearchHandbags_NullParameters_ReturnsOk()
    {
        // Arrange
        var handbags = new List<ListHandBagModel>
        {
            new ListHandBagModel { HandbagId = 1, ModelName = "Test Bag", BrandName = "Brand A" }
        }.AsQueryable();

        _mockHangBagService.Setup(s => s.SearchWithProjection(null, null)).Returns(handbags);

        // Act
        var result = _controller.SearchHandbags(null, null);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.NotNull(okResult.Value);
    }

    [Fact]
    public void UpdateHandbag_AsDeveloper_ThrowsUnauthorizedAccessException()
    {
        // Arrange
        SetupUserRole("developer");
        var model = new CreateHandBagModel
        {
            ModelName = "Updated Bag",
            Material = "Leather",
            Price = 150,
            Stock = 5,
            BrandId = 1
        };

        // Act & Assert
        var exception = Assert.Throws<UnauthorizedAccessException>(() => _controller.UpdateHandbag(1, model));
        Assert.Equal("Permission denied", exception.Message);
    }

    [Fact]
    public void UpdateHandbag_AsMember_ThrowsUnauthorizedAccessException()
    {
        // Arrange
        SetupUserRole("member");
        var model = new CreateHandBagModel
        {
            ModelName = "Updated Bag",
            Material = "Leather",
            Price = 150,
            Stock = 5,
            BrandId = 1
        };

        // Act & Assert
        var exception = Assert.Throws<UnauthorizedAccessException>(() => _controller.UpdateHandbag(1, model));
        Assert.Equal("Permission denied", exception.Message);
    }

    [Fact]
    public void UpdateHandbag_InvalidModelName_ThrowsFormatException()
    {
        // Arrange
        SetupUserRole("administrator");
        var model = new CreateHandBagModel
        {
            ModelName = "invalid model name", // lowercase start
            Material = "Leather",
            Price = 150,
            Stock = 5,
            BrandId = 1
        };

        // Act & Assert
        var exception = Assert.Throws<FormatException>(() => _controller.UpdateHandbag(1, model));
        Assert.Equal("modelName format is invalid", exception.Message);
    }

    [Fact]
    public void DeleteHandbag_AsDeveloper_ThrowsUnauthorizedAccessException()
    {
        // Arrange
        SetupUserRole("developer");

        // Act & Assert
        var exception = Assert.Throws<UnauthorizedAccessException>(() => _controller.DeleteHandbag(1));
        Assert.Equal("Permission denied", exception.Message);
    }

    [Fact]
    public void DeleteHandbag_AsMember_ThrowsUnauthorizedAccessException()
    {
        // Arrange
        SetupUserRole("member");

        // Act & Assert
        var exception = Assert.Throws<UnauthorizedAccessException>(() => _controller.DeleteHandbag(1));
        Assert.Equal("Permission denied", exception.Message);
    }
}
