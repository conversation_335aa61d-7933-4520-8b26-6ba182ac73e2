﻿
using DAL.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using Model;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using UOW;

namespace Services
{
    public class AuthService
    {
        private readonly UnitOfWork _unitOfWork;
        private readonly IConfiguration _configuration;

        public AuthService(UnitOfWork unitOfWork, IConfiguration configuration)
        {
            _unitOfWork = unitOfWork;   
            _configuration = configuration; 
        }

        public async Task<SystemAccount?> Login(LoginModel model)
        {
            var user = _unitOfWork.GetRepository<SystemAccount>()
                .Entities
                .FirstOrDefault(a => a.Email == model.Email && a.Password == model.Password);

            if (user == null || user.IsActive == false)
                return null; // Return null if login failed - Controller will handle

            string roleName = ConvertRoleToString(user.Role);
            if (roleName == null)
                return null; // Return null if role not authorized - Controller will handle

            return user;
        }
        private string? ConvertRoleToString(int? role)
        {
            return role switch
            {
                1 => "administrator",
                2 => "moderator",
                3 => "developer",
                4 => "member",
                _ => null
            };
        }

        public async Task<AuthResponse> GenerateJwtToken(string username, int? roleUser)
        {
            var role = ConvertRoleToString(roleUser);
            var claims = new[]
            {
                new Claim(JwtRegisteredClaimNames.Sub, username),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()), 
                new Claim(ClaimTypes.Role, role) 
            };

            var secretKey = Encoding.UTF8.GetBytes(_configuration["JwtSettings:Secret"]);

            var creds = new SigningCredentials(new SymmetricSecurityKey(secretKey), SecurityAlgorithms.HmacSha256);
            var token = new JwtSecurityToken(
                issuer: _configuration["JwtSettings:Issuer"],   
                audience: _configuration["JwtSettings:Audience"], 
                claims: claims,  
                expires: DateTime.Now.AddDays(1),  
                signingCredentials: creds   
            );

            return new AuthResponse 
            { 
                Token = new JwtSecurityTokenHandler().WriteToken(token),
                Role = role,
            };
        }

    }
}
