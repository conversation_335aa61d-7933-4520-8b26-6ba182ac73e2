# Quick Setup Guide - Flexible Postman Collection

## For Current Handbag API Project

### Option 1: Use Pre-configured Environment (Easiest)
1. Import `Postman_Collection_Handbag_API_Tests.json` into Postman
2. Import `Handbag_API_Environment.postman_environment.json` into Postman
3. Select the "Handbag API - PRN231_SU25_SE170115" environment
4. Run the collection - everything is pre-configured!

### Option 2: Manual Setup
1. Import the collection
2. Create a new environment with these variables:
   - `base_url`: `https://localhost:7176`
   - `resource_name`: `handbags`
   - `admin_email`: `<EMAIL>`
   - `admin_password`: `123456`
3. Run the collection

## For New Projects

### Quick Adaptation Steps:
1. Import `Postman_Collection_Handbag_API_Tests.json`
2. Import `Sample_Environment.postman_environment.json` as a template
3. Edit the environment variables:
   - Change `base_url` to your API URL
   - Change `resource_name` to your resource (e.g., "products", "users")
   - Update `create_item_body` and `update_item_body` with your API's JSON format
   - Update credentials if different
4. Run the collection

### Example: For a Products API
```
base_url: https://api.mystore.com
resource_name: products
create_item_body: {
  "name": "Test Product",
  "price": 99.99,
  "categoryId": 1
}
```

### Example: For a Users API
```
base_url: https://localhost:3000
api_path: api/v1
resource_name: users
auth_endpoint: login
create_item_body: {
  "username": "testuser",
  "email": "<EMAIL>",
  "role": "user"
}
```

## What You Get

✅ **10 Comprehensive Tests:**
- Login success/failure
- CRUD operations (Create, Read, Update, Delete)
- Authorization testing (admin vs member roles)
- Token validation
- Error handling

✅ **Automatic Features:**
- JWT token extraction and storage
- Dynamic ID extraction from responses
- Comprehensive response validation
- Flexible URL and body construction

✅ **Easy Customization:**
- Change only environment variables
- No need to edit individual requests
- Reusable across multiple projects

## Files Included

1. **`Postman_Collection_Handbag_API_Tests.json`** - The flexible test collection
2. **`Handbag_API_Environment.postman_environment.json`** - Pre-configured for Handbag API
3. **`Sample_Environment.postman_environment.json`** - Template for new projects
4. **`Postman_Collection_Usage_Guide.md`** - Detailed usage instructions
5. **`Quick_Setup_Guide.md`** - This quick reference

## Need Help?

- Check the detailed `Postman_Collection_Usage_Guide.md` for comprehensive instructions
- Verify your environment variables match your API structure
- Ensure your API is running and accessible at the specified `base_url`

## Pro Tips

🔥 **For Multiple Projects:** Create separate environments for each project and switch between them

🔥 **For Team Sharing:** Export your customized environment and share it with your team

🔥 **For CI/CD:** Use Newman (Postman CLI) to run these tests in your automated pipeline

🔥 **For Different Environments:** Create separate environments for dev, staging, and production
